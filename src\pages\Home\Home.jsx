import React, { useEffect } from "react";
import { Footer } from "../../components/Footer/Footer";
import { HeroSection } from "./components/HeroSection/HeroSection";
import { CategoriesSection } from "./components/CategoriesSection/CategoriesSection";
import { VideosSection } from "./components/VideosSection/VideosSection";
import { HeroVideo } from "./components/HeroVideo/HeroVideo";
import { useData } from "../../contexts/DataProvider.js";

export const Home = () => {
  const { loading } = useData();
  useEffect(() => {
    const hasReloaded = window.sessionStorage.getItem('hasReloaded');
    if (window.WidgetApp) {
      window.WidgetApp.mount("widget-app");
    } else {
      if (!hasReloaded) {
        window.sessionStorage.setItem("hasReloaded", "true");
        setTimeout(() => {
          window.sessionStorage.removeItem("hasReloaded");
          window.location.reload();
        }, 1000);
      }
    }
    return () => {
      if (window.WidgetApp) {
        window.WidgetApp.unmount("widget-app");
        if (hasReloaded) {
          window.sessionStorage.removeItem("hasReloaded");
        }
      }
    };
  }, []);

  return (
    !loading && (
      <>
        <div id="widget-app"></div>
        <div className="home-page">
          <div className="hero">
            <HeroVideo />
            <HeroSection />
            <VideosSection />
            <CategoriesSection />
            <Footer />
          </div>
        </div>
      </>
    )
  );
};
