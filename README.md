# Art Waves

Developed a responsive e-commerce platform for "Art Waves," a digital art marketplace, using ReactJS. The website allows artists to showcase and sell their digital artwork, while customers can browse, purchase, and securely checkout.

## Tech Stack

- Frontend: React.js, HTML, CSS
- Backend: MockBee
- Payment Integration: Razorpay
- Deployment: Netlify
- Other Libraries and Tools: React Router, Axios, JWT, etc.

## Features

- Authentication
  - Login
  - Logout
  - Signup
- Product Listing
- Filter Products by
  - Category
  - Discount
  - Price
  - Rating
- Cart Management
- Wishlist Management
- Search by
  - Product Name
  - Category
- Address Management
- Single Product Page
- Loading & Alerts
- User Profile Page
- Checkout
- Order Summary
- Order History
- Apply Coupons
- Payment Integration
- Responsive

## Run Locally

Install dependencies

```bash
  npm install
```

Start the server

```bash
  npm run start
```
