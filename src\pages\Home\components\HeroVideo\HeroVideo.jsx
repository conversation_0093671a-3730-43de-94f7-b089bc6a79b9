import "./HeroVideo.css";
import ReactPlayer from "react-player";
import { useNavigate } from "react-router-dom";

import React from "react";

export const HeroVideo = () => {
  const Navigate = useNavigate();
  const connectWallet = async () => {
    if (window.ethereums)
      await window.ethereums.request({ method: "eth_requestAccounts" });
  }
  return (
    <div className="hero-video-container">
      <div className="hero-video">
        <ReactPlayer
          url={`${process.env.PUBLIC_URL}/assets/videos/herovideo1.mp4`}
          playing
          playbackRate={1.5}
          muted
          loop
          controls={false}
          width={"100%"}
          height={"100%"}
        />
      </div>

      <div className="hero-text">
        <h1>Celebrate Creativity</h1>
        <h2>Where Pixels Become Masterpieces!</h2>
      </div>

      <div className="shop-btn-group">
        <button
          onClick={() => Navigate("product-listing")}
          className="shop-now-btn"
        >
          Shop Now
        </button>
        <button
          className="shop-now-btn connect-btn"
          id="widget-btn"
          onClick={connectWallet}
        >
          Connect
        </button>
      </div>
    </div>
  );
};
