.hero-video-container {
  display: flex;
  flex-flow: column nowrap;
  position: relative;
  height: 650px;
  background-color: black;
}

.hero-video {
  height: 100%;
  background-color: rgb(0, 0, 0);
  opacity: 0.4;
}

.hero-video-container video {
  object-fit: cover;
}

.hero-text {
  position: absolute;
  color: white;
  top: 10em;
  text-align: center;
  align-self: center;
}

.hero-text h1 {
  font-size: 3.5em;
  align-self: center;
}

.hero-text h2 {
  margin-top: 40px;
  font-size: 1.2em;
  opacity: 0.8;
}

.shop-now-btn {
  padding: 1.1rem 3.5rem;
  font-weight: bolder;
  font-size: 2rem;
  background: transparent;
  border: none;
  position: absolute;
  bottom: 10rem;
  align-self: center;
  justify-self: stretch;
  z-index: 1;
}

.shop-btn-group {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.connect-btn {
  right: 20px;
}

.shop-now-btn::after,
.shop-now-btn::before {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: -99999;
  transition: all .4s;
}

.shop-now-btn::before {
  transform: translate(0%, 0%);
  width: 100%;
  height: 100%;
  background: #a8ff78;
  background: -webkit-linear-gradient(to right, #78ffd6, #a8ff78);
  background: linear-gradient(to right, #78ffd6, #a8ff78);
  border-radius: 50px;
}

.shop-now-btn::after {
  transform: translate(0px, 0px);
  width: 10px;
  height: 10px;
  background: #FF416C;
  background: -webkit-linear-gradient(to right, #FF4B2B, #FF416C);
  background: linear-gradient(to right, #FF4B2B, #FF416C);
  backdrop-filter: blur(5px);
  border-radius: 50px;
}

.shop-now-btn:hover {
  color: white;
}

.shop-now-btn:hover::before {
  transform: translate(5%, 20%);
  width: 10px;
  height: 10px;
  background: #a8ff78;
  background: -webkit-linear-gradient(to right, #78ffd6, #a8ff78);
  background: linear-gradient(to right, #78ffd6, #a8ff78);
}

.shop-now-btn:hover::after {
  border-radius: 50px;
  transform: translate(0, 0);
  width: 100%;
  height: 100%;
  background: #FF416C;
  background: -webkit-linear-gradient(to right, #FF4B2B, #FF416C);
  background: linear-gradient(to right, #FF4B2B, #FF416C);
}

.shop-now-btn:active::after {
  transition: 0s;
  transform: translate(0, 5%);
}


@media (min-width: 768px) {
  .hero-video-container {
    height: 810px;
  }

  .hero-text h1 {
    font-size: 6em;
    align-self: center;
  }

  .hero-text h2 {
    margin-top: 40px;
    font-size: 1.5em;
    opacity: 0.8;
  }

  

  /* .shop-btn-group {

  } */
}

@media (max-width: 768px) {
  .shop-now-btn {
    width: 100%;
  }

  .connect-btn {
    right: auto;
    top: 40rem;
  }

  .shop-btn-group {
    padding: 0;
  }
}