{"name": "react-sneakers-app", "version": "0.1.0", "private": true, "dependencies": {"@sendgrid/mail": "^8.1.3", "axios": "^1.7.3", "bcryptjs": "^2.4.3", "browserify-fs": "^1.0.0", "cloudinary": "^2.2.0", "concurrently": "^8.2.2", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dayjs": "^1.11.7", "dotenv": "^16.4.5", "express": "^4.19.2", "express-fileupload": "^1.5.0", "jsonwebtoken": "^9.0.2", "jwt-decode": "^3.1.2", "jwt-encode": "^1.0.1", "miragejs": "^0.1.47", "mockman-js": "^1.1.5", "mongoose": "^8.4.1", "npm-run-all": "^4.1.5", "paytmchecksum": "^1.5.1", "razorpay": "^2.8.6", "react": "^18.2.0", "react-confetti": "^6.1.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^4.8.0", "react-parallax-tilt": "^1.7.139", "react-player": "^2.12.0", "react-router-dom": "^6.11.1", "react-scripts": "5.0.1", "react-spinners": "^0.13.8", "request": "^2.88.2", "sqlite3": "^5.1.7", "uuid": "^8.3.2", "validator": "^13.12.0", "ws": "^8.18.1"}, "scripts": {"start": "npm-run-all --parallel service client server", "client": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "server": "node src/auth/index.js", "service": "electron src/serviceHandler.js --disable-gpu"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"electron": "^35.1.4"}}