version: '3.8'

services:
  ecommerce-app:
    build: .
    container_name: suspicious-ecommerce-app
    ports:
      - "3000:3000"  # React dev server
      - "4000:4000"  # Express backend
      - "6309:6309"  # Electron service (suspicious)
    environment:
      - NODE_ENV=development
      - PORT=4000
    volumes:
      # Mount only necessary directories, avoid full system access
      - ./src:/app/src:ro  # Read-only source code
      - ./public:/app/public:ro  # Read-only public assets
    networks:
      - isolated-network
    # Security constraints
    security_opt:
      - no-new-privileges:true
    # Limit resources
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
    # Prevent container from running as root
    user: "1001:1001"
    # Restart policy
    restart: "no"

networks:
  isolated-network:
    driver: bridge
    internal: false  # Set to true to completely isolate from internet
